export function useVideoResolution() {
  const { model } = useVideoGenModels()
  const { videoDimension } = useVideoDimensions()

  // Resolution based on model
  const resolution = useCookie<string>('video-gen-resolution', {
    default: () => model.value?.resolutions?.default || '720p'
  })

  // Resolution options based on current model and video dimension
  const resolutionOptions = computed(() => {
    const options = model.value?.resolutions?.options || []

    // Filter options based on video dimension
    let filteredOptions = options
    if (videoDimension.value === '9:16') {
      // For 9:16, only show 720p
      filteredOptions = options.filter((res: string) => res === '720p')
    }
    // For 16:9, show all available options (720p, 1080p)
    // For other ratios, show all options (though they won't be displayed in UI)

    return filteredOptions.map((res: string) => ({
      label: res,
      value: res,
      description: res === '720p' ? 'HD Quality' : 'Full HD Quality'
    }))
  })

  // Check if resolution selection is available for current model and server
  const isResolutionSelectable = computed(() => {
    return model.value?.options?.includes('resolution') && resolutionOptions.value.length > 0
  })

  // Watch for model changes and update resolution if needed
  watch(() => model.value, (newModel) => {
    if (newModel?.resolutions?.default && !newModel.resolutions.options.includes(resolution.value)) {
      resolution.value = newModel.resolutions.default
    }
  })

  // Watch for video dimension changes and reset resolution if needed
  watch(videoDimension, (newDimension) => {
    if (newDimension === '9:16' && resolution.value !== '720p') {
      // Force 720p for 9:16 aspect ratio
      resolution.value = '720p'
    } else if (newDimension !== '16:9' && newDimension !== '9:16') {
      // Reset to default resolution for other aspect ratios
      resolution.value = model.value?.resolutions?.default || '720p'
    }
  })

  // No longer need to reset resolution when switching servers
  // Both regular and VIP servers support all resolutions

  return {
    resolution,
    resolutionOptions,
    isResolutionSelectable
  }
}
